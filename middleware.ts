import { type NextRequest, NextResponse } from "next/server";
import { updateSession } from "@/supabase/client/middleware";

export async function middleware(request: NextRequest) {
  // Skip middleware for static assets and API routes
  if (
    request.nextUrl.pathname.startsWith("/_next") ||
    request.nextUrl.pathname.startsWith("/api") ||
    request.nextUrl.pathname.match(/\.(?:svg|png|jpg|jpeg|gif|webp)$/)
  ) {
    return;
  }

  // Create a base response that will be returned to the client.
  const response = NextResponse.next();

  // Forward the request/response pair to our Supabase helper.
  return await updateSession(request, response);
}

// Apply middleware to specific paths, including dashboard pages
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api/auth (API auth routes)
     * It WILL match /profile, /home, /engage, /auth/callback etc.
     */
    '/((?!_next/static|_next/image|favicon.ico|api/auth|assets|images|.*\.(?:svg|png|jpg|jpeg|gif|webp)$).+)',
    // Include the root path to ensure middleware runs for the landing page
    '/',
    // Include auth callback path explicitly to ensure middleware runs after authentication
    '/auth/callback',
  ],
};
