'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
// Define types locally since engage functionality is not implemented yet
type DiscoveryMode =
  | 'search_based'
  | 'timeline_for_you'
  | 'timeline_following'
  | 'user_acquisition'
  | 'hashtag_monitoring'
  | 'competitor_monitoring'
  | 'event_monitoring';

type ModeAvailability = Record<DiscoveryMode, boolean>;

// Stub functions for discovery actions (to be implemented later)
const runSearchBasedDiscovery = async (platform: string, query: string) => ({ success: false, message: 'Not implemented', taskId: null, liveUrl: null });
const runForYouTimelineDiscovery = async (platform: string) => ({ success: false, message: 'Not implemented', taskId: null, liveUrl: null });
const runFollowingTimelineDiscovery = async (platform: string) => ({ success: false, message: 'Not implemented', taskId: null, liveUrl: null });
const runUserAcquisitionDiscovery = async (platform: string) => ({ success: false, message: 'Not implemented', taskId: null, liveUrl: null });
const runHashtagMonitoringDiscovery = async (platform: string) => ({ success: false, message: 'Not implemented', taskId: null, liveUrl: null });
const runCompetitorMonitoringDiscovery = async (platform: string) => ({ success: false, message: 'Not implemented', taskId: null, liveUrl: null });
const runEventMonitoringDiscovery = async (platform: string, eventName: string) => ({ success: false, message: 'Not implemented', taskId: null, liveUrl: null });

// Define an array of the possible DiscoveryMode values
const discoveryModes: DiscoveryMode[] = [
  'search_based',
  'timeline_for_you',
  'timeline_following',
  'user_acquisition',
  'hashtag_monitoring',
  'competitor_monitoring',
  'event_monitoring'
];

interface DiscoveryModeSelectorProps {
  platform: 'x' | 'linkedin' | 'reddit';
  onTaskStarted: (taskId: string, liveUrl: string | null, mode: DiscoveryMode) => void;
  isDiscovering: boolean;
  selectedMode: string;
  setSelectedMode: (mode: string) => void;
  modeAvailability: ModeAvailability | null;
}

export function DiscoveryModeSelector({
  platform,
  onTaskStarted,
  isDiscovering,
  selectedMode,
  setSelectedMode,
  modeAvailability
}: DiscoveryModeSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [eventName, setEventName] = useState('');
  const { toast } = useToast();

  const handleRunDiscovery = async () => {
    if (isDiscovering) {
      toast({
        title: "Already Running",
        description: "Please wait for the current discovery to finish.",
        variant: "destructive"
      });
      return;
    }

    try {
      let result;

      switch (selectedMode) {
        case 'search_based':
          result = await runSearchBasedDiscovery(platform, searchQuery);
          break;
        case 'timeline_for_you':
          result = await runForYouTimelineDiscovery(platform);
          break;
        case 'timeline_following':
          result = await runFollowingTimelineDiscovery(platform);
          break;
        case 'user_acquisition':
          result = await runUserAcquisitionDiscovery(platform);
          break;
        case 'hashtag_monitoring':
          result = await runHashtagMonitoringDiscovery(platform);
          break;
        case 'competitor_monitoring':
          result = await runCompetitorMonitoringDiscovery(platform);
          break;
        case 'event_monitoring':
          result = await runEventMonitoringDiscovery(platform, eventName);
          break;
        default:
          throw new Error(`Unknown discovery mode: ${selectedMode}`);
      }

      if (result.success && result.taskId) {
        toast({
          title: "Discovery Started",
          description: `${selectedMode} discovery task started successfully.`
        });
        onTaskStarted(result.taskId, result.liveUrl || null, selectedMode as DiscoveryMode);
      } else {
        throw new Error(result.message || "Failed to start discovery task.");
      }
    } catch (error: any) {
      console.error("Error starting discovery:", error);
      toast({
        title: "Discovery Failed",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive"
      });
    }
  };

  // Get the label for the current mode
  const getModeLabel = (mode: DiscoveryMode): string => {
    switch (mode) {
      case 'search_based': return 'Search-Based Engagement';
      case 'timeline_for_you': return 'Timeline: For You';
      case 'timeline_following': return 'Timeline: Following';
      case 'user_acquisition': return 'Proactive User Acquisition';
      case 'hashtag_monitoring': return 'Hashtag Monitoring';
      case 'competitor_monitoring': return 'Competitor Monitoring';
      case 'event_monitoring': return 'Event Monitoring';
      default: return mode;
    }
  };

  // Get the description for the current mode
  const getModeDescription = (mode: DiscoveryMode): string => {
    switch (mode) {
      case 'search_based':
        return 'Find tweets based on search keywords that are good candidates for engagement.';
      case 'timeline_for_you':
        return 'Browse your "For You" timeline to find relevant tweets to engage with.';
      case 'timeline_following':
        return 'Browse your "Following" timeline to find tweets from accounts you follow.';
      case 'user_acquisition':
        return 'Find potential customers discussing problems your product could solve.';
      case 'hashtag_monitoring':
        return 'Monitor specific hashtags to find relevant conversations.';
      case 'competitor_monitoring':
        return 'Monitor conversations about competitors to find engagement opportunities.';
      case 'event_monitoring':
        return 'Monitor conversations about industry events to find networking opportunities.';
      default:
        return '';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Discovery Mode</CardTitle>
        <CardDescription>
          Select how you want to discover content on {platform === 'x' ? 'X.com' : platform}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="discovery-mode">Mode</Label>
          <Select
            value={selectedMode}
            onValueChange={(value) => setSelectedMode(value as DiscoveryMode)}
            disabled={isDiscovering}
          >
            <SelectTrigger id="discovery-mode">
              <SelectValue placeholder="Select mode" />
            </SelectTrigger>
            <SelectContent>
              {/* Iterate over the actual discoveryModes array */}
              {discoveryModes.map((mode) => {
                // Check availability using the mode value as the key
                const isAvailable = modeAvailability ? modeAvailability[mode] : false;
                const isDisabled = isDiscovering || !isAvailable;
                return (
                  <SelectItem
                    key={mode} // Use mode as key
                    value={mode} // Use mode as value
                    disabled={isDisabled}
                  >
                    {/* Add Tooltip later if needed */}
                    {/* {isDisabled && !isLoadingProfile && <Lock size={12} className="inline mr-1 opacity-50" />} */}
                    {/* Display formatted mode label using the getModeLabel helper */}
                    {getModeLabel(mode)}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        <div className="p-4 bg-muted/50 rounded-md">
          <p className="text-sm">{getModeDescription(selectedMode as DiscoveryMode)}</p>
        </div>

        {/* Mode-specific inputs */}
        {selectedMode === 'search_based' && (
          <div className="space-y-2">
            <Label htmlFor="search-query">Search Query</Label>
            <Input
              id="search-query"
              placeholder="Enter search keywords"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              disabled={isDiscovering}
            />
            <p className="text-xs text-muted-foreground">
              Leave blank to use your profile interests
            </p>
          </div>
        )}

        {selectedMode === 'event_monitoring' && (
          <div className="space-y-2">
            <Label htmlFor="event-name">Event Name</Label>
            <Input
              id="event-name"
              placeholder="Enter event name"
              value={eventName}
              onChange={(e) => setEventName(e.target.value)}
              disabled={isDiscovering}
            />
            <p className="text-xs text-muted-foreground">
              Leave blank to use your upcoming events
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleRunDiscovery}
          disabled={isDiscovering}
          className="w-full"
        >
          {isDiscovering ? 'Running...' : `Start ${getModeLabel(selectedMode as DiscoveryMode)}`}
        </Button>
      </CardFooter>
    </Card>
  );
}
