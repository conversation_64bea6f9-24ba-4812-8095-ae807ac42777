'use client';

import React, { useEffect, useState } from "react";
import Select from "@/components/ui/select-animated";
import { Search, Clock, Users, UserPlus, Hash, Activity, Calendar, Bot } from "lucide-react";

// Define the DiscoveryMode type
type DiscoveryMode =
  | "search_based"
  | "timeline_for_you"
  | "timeline_following"
  | "user_acquisition"
  | "hashtag_monitoring"
  | "competitor_monitoring"
  | "event_monitoring";

// Define the data structure for our select component
type DiscoveryModeOption = {
  id: string;
  label: string;
  value: DiscoveryMode;
  description: string;
  icon: React.ReactNode;
};

// Create the discovery mode options
const discoveryModeOptions: DiscoveryModeOption[] = [
  {
    id: "1",
    label: "Search-Based Engagement",
    value: "search_based",
    description: "Find tweets based on search keywords that are good candidates for engagement",
    icon: <Search size={18} />,
  },
  {
    id: "2",
    label: "Timeline: For You",
    value: "timeline_for_you",
    description: "Browse your 'For You' timeline to find relevant tweets to engage with",
    icon: <Clock size={18} />,
  },
  {
    id: "3",
    label: "Timeline: Following",
    value: "timeline_following",
    description: "Browse your 'Following' timeline to find tweets from accounts you follow",
    icon: <Users size={18} />,
  },
  {
    id: "4",
    label: "User Acquisition",
    value: "user_acquisition",
    description: "Find potential customers discussing problems your product could solve",
    icon: <UserPlus size={18} />,
  },
  {
    id: "5",
    label: "Hashtag Monitoring",
    value: "hashtag_monitoring",
    description: "Monitor specific hashtags to find relevant conversations",
    icon: <Hash size={18} />,
  },
  {
    id: "6",
    label: "Competitor Monitoring",
    value: "competitor_monitoring",
    description: "Monitor conversations about competitors to find engagement opportunities",
    icon: <Activity size={18} />,
  },
  {
    id: "7",
    label: "Event Monitoring",
    value: "event_monitoring",
    description: "Monitor conversations about industry events to find networking opportunities",
    icon: <Calendar size={18} />,
  },
];

interface DiscoveryModeSelectProps {
  defaultMode?: DiscoveryMode;
  onChange?: (mode: DiscoveryMode) => void;
  disabled?: boolean;
}

export function DiscoveryModeSelect({
  defaultMode = "search_based",
  onChange,
  disabled = false,
}: DiscoveryModeSelectProps) {
  const [selectedMode, setSelectedMode] = useState<DiscoveryMode>(defaultMode);

  // Handle selection change
  const handleChange = (value: string) => {
    const mode = value as DiscoveryMode;
    setSelectedMode(mode);
    if (onChange) {
      onChange(mode);
    }
  };

  // Update selected mode when defaultMode changes
  useEffect(() => {
    setSelectedMode(defaultMode);
  }, [defaultMode]);

  return (
    <div className={`${disabled ? 'opacity-50 pointer-events-none' : ''}`}>
      <Select
        data={discoveryModeOptions}
        defaultValue={selectedMode}
        onChange={handleChange}
      />
    </div>
  );
}
