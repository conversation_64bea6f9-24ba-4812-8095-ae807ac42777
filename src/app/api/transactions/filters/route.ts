import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface FilterOptionsResponse {
  platforms: FilterOption[];
  merchants: FilterOption[];
  statuses: FilterOption[];
  error?: string;
}

export async function GET(request: NextRequest) {
  try {
    // Validate database connection first
    const { data: healthCheck, error: healthError } = await supabaseAdmin
      .from('normalized_transactions')
      .select('id')
      .limit(1);

    if (healthError) {
      console.error('Database connection error:', healthError);
      return NextResponse.json(
        { error: 'Database connection failed', details: healthError.message },
        { status: 503 }
      );
    }

    // Get distinct platforms with counts
    const { data: platformData, error: platformError } = await supabaseAdmin
      .from('normalized_transactions')
      .select('platform')
      .not('platform', 'is', null);

    if (platformError) {
      console.error('Platform query error:', platformError);
      return NextResponse.json(
        { error: 'Failed to fetch platform options', details: platformError.message },
        { status: 500 }
      );
    }

    // Get distinct merchants with counts
    const { data: merchantData, error: merchantError } = await supabaseAdmin
      .from('normalized_transactions')
      .select('merchant_name')
      .not('merchant_name', 'is', null);

    if (merchantError) {
      console.error('Merchant query error:', merchantError);
      return NextResponse.json(
        { error: 'Failed to fetch merchant options', details: merchantError.message },
        { status: 500 }
      );
    }

    // Get distinct statuses with counts
    const { data: statusData, error: statusError } = await supabaseAdmin
      .from('normalized_transactions')
      .select('status')
      .not('status', 'is', null);

    if (statusError) {
      console.error('Status query error:', statusError);
      return NextResponse.json(
        { error: 'Failed to fetch status options', details: statusError.message },
        { status: 500 }
      );
    }

    // Process platforms
    const platformCounts = platformData.reduce((acc: Record<string, number>, item) => {
      const platform = item.platform;
      acc[platform] = (acc[platform] || 0) + 1;
      return acc;
    }, {});

    const platforms: FilterOption[] = Object.entries(platformCounts)
      .map(([value, count]) => ({
        value,
        label: value === 'strackr' ? 'Strackr' : value.charAt(0).toUpperCase() + value.slice(1),
        count
      }))
      .sort((a, b) => b.count - a.count);

    // Process merchants (limit to top 50 by transaction count)
    const merchantCounts = merchantData.reduce((acc: Record<string, number>, item) => {
      const merchant = item.merchant_name;
      acc[merchant] = (acc[merchant] || 0) + 1;
      return acc;
    }, {});

    const merchants: FilterOption[] = Object.entries(merchantCounts)
      .map(([value, count]) => ({
        value,
        label: value,
        count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 50); // Limit to top 50 merchants

    // Process statuses
    const statusCounts = statusData.reduce((acc: Record<string, number>, item) => {
      const status = item.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const statuses: FilterOption[] = Object.entries(statusCounts)
      .map(([value, count]) => ({
        value,
        label: value.charAt(0).toUpperCase() + value.slice(1),
        count
      }))
      .sort((a, b) => b.count - a.count);

    const response: FilterOptionsResponse = {
      platforms,
      merchants,
      statuses
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Filter options API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
